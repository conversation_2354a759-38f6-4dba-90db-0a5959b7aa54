import torch
import sys
import urllib.request
from types import ModuleType

class NoSegmentRequest(urllib.request.OpenerDirector):
    def open(self, req, *args, **kwargs):
        if 'segment.io' in req.get_full_url():
            raise urllib.request.URLError('Segment.io blocked')
        return super().open(req, *args, **kwargs)

def apply_torch_patch():
    # 阻止Segment.io请求
    opener = NoSegmentRequest()
    opener.add_handler(urllib.request.HTTPHandler())
    urllib.request.install_opener(opener)
    
    # 更安全的torch补丁 - 仅当模块存在时才应用
    if 'torch' in sys.modules:
        torch_module = sys.modules['torch']
        if hasattr(torch_module, '_classes'):
            try:
                # 更安全的处理方式
                torch_module._classes = ModuleType('torch._classes')
                torch_module._classes.__path__ = []
            except Exception:
                pass  # 如果无法修改则忽略

apply_torch_patch()