Looking in indexes: https://pypi.tuna.tsinghua.edu.cn/simple
Requirement already satisfied: torch in e:\anaconda\envs\spark\lib\site-packages (2.7.0)
Requirement already satisfied: streamlit in e:\anaconda\envs\spark\lib\site-packages (1.45.1)
Requirement already satisfied: filelock in e:\anaconda\envs\spark\lib\site-packages (from torch) (3.18.0)
Requirement already satisfied: typing-extensions>=4.10.0 in e:\anaconda\envs\spark\lib\site-packages (from torch) (4.13.2)
Requirement already satisfied: sympy>=1.13.3 in e:\anaconda\envs\spark\lib\site-packages (from torch) (1.14.0)
Requirement already satisfied: networkx in e:\anaconda\envs\spark\lib\site-packages (from torch) (3.2.1)
Requirement already satisfied: jinja2 in e:\anaconda\envs\spark\lib\site-packages (from torch) (3.1.6)
Requirement already satisfied: fsspec in e:\anaconda\envs\spark\lib\site-packages (from torch) (2025.5.1)
Requirement already satisfied: altair<6,>=4.0 in e:\anaconda\envs\spark\lib\site-packages (from streamlit) (5.5.0)
Requirement already satisfied: blinker<2,>=1.5.0 in e:\anaconda\envs\spark\lib\site-packages (from streamlit) (1.9.0)
Requirement already satisfied: cachetools<6,>=4.0 in e:\anaconda\envs\spark\lib\site-packages (from streamlit) (5.5.2)
Requirement already satisfied: click<9,>=7.0 in e:\anaconda\envs\spark\lib\site-packages (from streamlit) (8.1.8)
Requirement already satisfied: numpy<3,>=1.23 in e:\anaconda\envs\spark\lib\site-packages (from streamlit) (2.0.2)
Requirement already satisfied: packaging<25,>=20 in e:\anaconda\envs\spark\lib\site-packages (from streamlit) (24.2)
Requirement already satisfied: pandas<3,>=1.4.0 in e:\anaconda\envs\spark\lib\site-packages (from streamlit) (2.2.3)
Requirement already satisfied: pillow<12,>=7.1.0 in e:\anaconda\envs\spark\lib\site-packages (from streamlit) (11.2.1)
Requirement already satisfied: protobuf<7,>=3.20 in e:\anaconda\envs\spark\lib\site-packages (from streamlit) (5.29.5)
Requirement already satisfied: pyarrow>=7.0 in e:\anaconda\envs\spark\lib\site-packages (from streamlit) (20.0.0)
Requirement already satisfied: requests<3,>=2.27 in e:\anaconda\envs\spark\lib\site-packages (from streamlit) (2.32.3)
Requirement already satisfied: tenacity<10,>=8.1.0 in e:\anaconda\envs\spark\lib\site-packages (from streamlit) (9.1.2)
Requirement already satisfied: toml<2,>=0.10.1 in e:\anaconda\envs\spark\lib\site-packages (from streamlit) (0.10.2)
Requirement already satisfied: watchdog<7,>=2.1.5 in e:\anaconda\envs\spark\lib\site-packages (from streamlit) (6.0.0)
Requirement already satisfied: gitpython!=3.1.19,<4,>=3.0.7 in e:\anaconda\envs\spark\lib\site-packages (from streamlit) (3.1.44)
Requirement already satisfied: pydeck<1,>=0.8.0b4 in e:\anaconda\envs\spark\lib\site-packages (from streamlit) (0.9.1)
Requirement already satisfied: tornado<7,>=6.0.3 in e:\anaconda\envs\spark\lib\site-packages (from streamlit) (6.5.1)
Requirement already satisfied: jsonschema>=3.0 in e:\anaconda\envs\spark\lib\site-packages (from altair<6,>=4.0->streamlit) (4.24.0)
Requirement already satisfied: narwhals>=1.14.2 in e:\anaconda\envs\spark\lib\site-packages (from altair<6,>=4.0->streamlit) (1.41.0)
Requirement already satisfied: colorama in e:\anaconda\envs\spark\lib\site-packages (from click<9,>=7.0->streamlit) (0.4.6)
Requirement already satisfied: gitdb<5,>=4.0.1 in e:\anaconda\envs\spark\lib\site-packages (from gitpython!=3.1.19,<4,>=3.0.7->streamlit) (4.0.12)
Requirement already satisfied: smmap<6,>=3.0.1 in e:\anaconda\envs\spark\lib\site-packages (from gitdb<5,>=4.0.1->gitpython!=3.1.19,<4,>=3.0.7->streamlit) (5.0.2)
Requirement already satisfied: python-dateutil>=2.8.2 in e:\anaconda\envs\spark\lib\site-packages (from pandas<3,>=1.4.0->streamlit) (2.9.0.post0)
Requirement already satisfied: pytz>=2020.1 in e:\anaconda\envs\spark\lib\site-packages (from pandas<3,>=1.4.0->streamlit) (2025.2)
Requirement already satisfied: tzdata>=2022.7 in e:\anaconda\envs\spark\lib\site-packages (from pandas<3,>=1.4.0->streamlit) (2025.2)
Requirement already satisfied: charset-normalizer<4,>=2 in e:\anaconda\envs\spark\lib\site-packages (from requests<3,>=2.27->streamlit) (3.4.2)
Requirement already satisfied: idna<4,>=2.5 in e:\anaconda\envs\spark\lib\site-packages (from requests<3,>=2.27->streamlit) (3.10)
Requirement already satisfied: urllib3<3,>=1.21.1 in e:\anaconda\envs\spark\lib\site-packages (from requests<3,>=2.27->streamlit) (2.4.0)
Requirement already satisfied: certifi>=2017.4.17 in e:\anaconda\envs\spark\lib\site-packages (from requests<3,>=2.27->streamlit) (2025.4.26)
Requirement already satisfied: MarkupSafe>=2.0 in e:\anaconda\envs\spark\lib\site-packages (from jinja2->torch) (3.0.2)
Requirement already satisfied: attrs>=22.2.0 in e:\anaconda\envs\spark\lib\site-packages (from jsonschema>=3.0->altair<6,>=4.0->streamlit) (25.3.0)
Requirement already satisfied: jsonschema-specifications>=2023.03.6 in e:\anaconda\envs\spark\lib\site-packages (from jsonschema>=3.0->altair<6,>=4.0->streamlit) (2025.4.1)
Requirement already satisfied: referencing>=0.28.4 in e:\anaconda\envs\spark\lib\site-packages (from jsonschema>=3.0->altair<6,>=4.0->streamlit) (0.36.2)
Requirement already satisfied: rpds-py>=0.7.1 in e:\anaconda\envs\spark\lib\site-packages (from jsonschema>=3.0->altair<6,>=4.0->streamlit) (0.25.1)
Requirement already satisfied: six>=1.5 in e:\anaconda\envs\spark\lib\site-packages (from python-dateutil>=2.8.2->pandas<3,>=1.4.0->streamlit) (1.17.0)
Requirement already satisfied: mpmath<1.4,>=1.1.0 in e:\anaconda\envs\spark\lib\site-packages (from sympy>=1.13.3->torch) (1.3.0)
