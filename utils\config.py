import os
from dotenv import load_dotenv
from langchain_community.llms import SparkLLM
import urllib3
import socket

load_dotenv()

# 全局超时设置（单位：秒）
DEFAULT_TIMEOUT = 120  
MAX_RETRIES = 3

# 配置底层网络超时
socket.setdefaulttimeout(DEFAULT_TIMEOUT)
urllib3.disable_warnings()
http = urllib3.PoolManager(timeout=urllib3.Timeout(connect=DEFAULT_TIMEOUT, read=DEFAULT_TIMEOUT))

def get_spark_llm():
    """
    获取SparkLLM实例，带有全面的超时控制
    """
    return SparkLLM(
        spark_app_id=os.getenv("SPARK_APP_ID"),
        spark_api_key=os.getenv("SPARK_API_KEY"),
        spark_api_secret=os.getenv("SPARK_API_SECRET"),
        spark_llm_domain="4.0Ultra",
        spark_api_url="wss://spark-api.xf-yun.com/v4.0/chat",
        request_timeout=DEFAULT_TIMEOUT,
        streaming=False,  # 禁用流式以增加稳定性
        max_retries=MAX_RETRIES,
        metadata={"timeout": str(DEFAULT_TIMEOUT)},
        timeout=DEFAULT_TIMEOUT,  # 显式设置内部超时
        call_timeout=DEFAULT_TIMEOUT  # 双重保险设置调用超时
    )