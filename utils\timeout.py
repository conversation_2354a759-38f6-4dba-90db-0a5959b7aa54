import platform
from functools import wraps
from threading import Thread
import time

class TimeoutError(Exception):
    pass

if platform.system() != 'Windows':
    # Unix/Linux/Mac 使用信号实现
    import signal
    
    def timeout(seconds=120, error_message="Function call timed out"):
        def decorator(func):
            def _handle_timeout(signum, frame):
                raise TimeoutError(error_message)
            
            @wraps(func)
            def wrapper(*args, **kwargs):
                signal.signal(signal.SIGALRM, _handle_timeout)
                signal.alarm(seconds)
                try:
                    result = func(*args, **kwargs)
                finally:
                    signal.alarm(0)
                return result
            return wrapper
        return decorator
else:
    # Windows 使用线程实现
    def timeout(seconds=120, error_message="Function call timed out"):
        def decorator(func):
            @wraps(func)
            def wrapper(*args, **kwargs):
                result = None
                exception = None
                
                def target():
                    nonlocal result, exception
                    try:
                        result = func(*args, **kwargs)
                    except Exception as e:
                        exception = e
                
                thread = Thread(target=target)
                thread.start()
                thread.join(seconds)
                
                if thread.is_alive():
                    # 线程仍在运行，说明超时
                    raise TimeoutError(error_message)
                elif exception is not None:
                    raise exception
                else:
                    return result
            return wrapper
        return decorator