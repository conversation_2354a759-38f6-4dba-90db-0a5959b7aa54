import time
from utils.config import get_spark_llm
from utils.timeout import timeout, TimeoutError

@timeout(seconds=120)
def test_long_running_query():
    llm = get_spark_llm()
    start = time.time()
    try:
        # 优化后的提示词（分步请求）
        prompt = """请分步描述人工智能发展史，先列出5个最重要的里程碑事件：
        1. 每个事件名称和年份
        2. 每个事件100字左右的说明
        3. 后续可以继续请求更多事件"""
        
        # 使用推荐的invoke方法
        response = llm.invoke(prompt)  # 替换原来的__call__
        
        print(f"成功！耗时: {time.time()-start:.2f}秒")
        print(response[:500] + "...")
    except TimeoutError as e:
        print(f"测试超时！耗时: {time.time()-start:.2f}秒")
        print(f"错误详情: {str(e)}")
        print("解决方案: 请简化请求内容或联系API提供商增加超时限制")
    except Exception as e:
        print(f"其他错误！耗时: {time.time()-start:.2f}秒")
        print(f"错误类型: {type(e).__name__}")
        print(f"错误信息: {str(e)}")
        if "timeout" in str(e).lower():
            print("提示: 检测到超时错误，请检查config.py中的超时设置")

if __name__ == "__main__":
    print("=== 开始超时测试 ===")
    test_long_running_query()
    print("=== 测试结束 ===")