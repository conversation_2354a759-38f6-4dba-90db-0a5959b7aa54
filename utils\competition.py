import re
import json
import logging
from datetime import datetime
from utils.config import get_spark_llm
from utils.timeout import timeout

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

MAX_INPUT_LENGTH = 3500  # 预留500字符给提示词

@timeout(seconds=120)
def safe_llm_call(prompt):
    """安全的LLM调用，带有超时和长度验证"""
    llm = get_spark_llm()
    try:
        # 添加长度验证
        if len(prompt) > MAX_INPUT_LENGTH:
            raise ValueError(f"输入长度超过{MAX_INPUT_LENGTH}字符限制")
        
        logger.info(f"发送给LLM的提示词长度: {len(prompt)}字符")
        response = llm.invoke(prompt)
        logger.info(f"收到LLM响应长度: {len(response)}字符")
        
        return response.strip()  # 去除前后空白
    except Exception as e:
        logger.error(f"LLM调用失败: {str(e)}")
        if "exceed limit" in str(e).lower():
            raise ValueError("输入内容过长，请简化文本") from e
        raise Exception(f"LLM调用失败: {str(e)}") from e

def preprocess_text(text):
    """预处理文本以适应API限制"""
    # 移除多余空格和换行
    text = re.sub(r'\s+', ' ', text).strip()
    
    # 关键信息提取（非LLM方式）
    key_sections = []
    for line in text.split('。'):
        if any(keyword in line for keyword in ["竞赛", "比赛", "报名", "奖项", "主办"]):
            key_sections.append(line)
        if len('。'.join(key_sections)) > MAX_INPUT_LENGTH * 0.7:  # 保留30%空间给提示词
            break
    
    processed_text = '。'.join(key_sections)[:MAX_INPUT_LENGTH]
    logger.info(f"预处理后文本长度: {len(processed_text)}字符")
    return processed_text

def validate_and_fix_json(json_str):
    """验证并尝试修复JSON格式"""
    try:
        # 尝试直接解析
        return json.loads(json_str)
    except json.JSONDecodeError as e:
        logger.warning(f"初始JSON解析失败，尝试修复: {str(e)}")
        
        # 尝试提取JSON部分（如果响应包含非JSON内容）
        json_match = re.search(r'\{.*\}', json_str, re.DOTALL)
        if json_match:
            json_str = json_match.group(0)
            logger.info(f"提取到疑似JSON部分: {json_str[:200]}...")
        
        # 常见修复：去除可能包裹JSON的Markdown代码块
        json_str = re.sub(r'^```json|```$', '', json_str, flags=re.IGNORECASE).strip()
        
        # 常见修复：处理多余的逗号
        json_str = re.sub(r',\s*([}\]])', r'\1', json_str)
        
        # 常见修复：处理单引号
        json_str = json_str.replace("'", '"')
        
        try:
            return json.loads(json_str)
        except json.JSONDecodeError as e:
            logger.error(f"无法修复的JSON格式: {str(e)}")
            raise ValueError(f"无法解析LLM返回的JSON格式: {str(e)}") from e

def extract_competition_info(text):
    """从文本中提取竞赛信息"""
    try:
        # 预处理文本
        processed_text = preprocess_text(text)
        if len(processed_text) < 50:
            return {
                "error": "有效文本过短",
                "solution": "请提供包含竞赛名称、主办方等关键信息的文本",
                "actual_length": len(text)
            }

        prompt = f"""请从以下文本中严格提取关键竞赛信息，并以纯JSON格式返回（不要包含任何额外文本或解释）:

{processed_text}

返回格式必须严格遵循以下JSON结构（包括所有字段，即使值为空）:
{{
  "竞赛名称": "字符串",
  "主办方": "字符串",
  "关键日期": {{
    "报名截止": "YYYY-MM-DD格式或文本描述",
    "比赛时间": "YYYY-MM-DD格式或文本描述"
  }},
  "核心要求": ["条目1", "条目2"],
  "主要奖项": ["奖项1", "奖项2"]
}}

注意:
1. 只返回JSON对象，不要有任何额外文本
2. 确保所有引号是双引号
3. 确保JSON格式完全正确
4. 如果某些信息缺失，使用空字符串或空数组"""

        logger.info("发送给LLM的提示词已构建")
        response = safe_llm_call(prompt)
        
        # 验证响应
        if not response:
            raise ValueError("LLM返回了空响应")
            
        logger.info(f"原始LLM响应: {response[:200]}...")
        
        # 解析JSON
        result = validate_and_fix_json(response)
        
        # 验证必要字段
        if not isinstance(result, dict):
            raise ValueError("返回的不是有效的JSON对象")
            
        if not result.get("竞赛名称"):
            raise ValueError("未提取到竞赛名称")
            
        logger.info("成功提取竞赛信息")
        return result

    except ValueError as e:
        logger.error(f"输入限制错误: {str(e)}")
        return {
            "error": f"输入限制: {str(e)}",
            "solution": f"请将文档内容缩短至{MAX_INPUT_LENGTH}字符内",
            "actual_length": len(text)
        }
    except Exception as e:
        logger.error(f"处理失败: {str(e)}", exc_info=True)
        return {
            "error": f"处理失败: {str(e)}",
            "solution": "请尝试：1. 上传更短的文档 2. 直接粘贴关键部分 3. 分多次提取",
            "type": type(e).__name__
        }