# 智能助手项目技术栈文档

## 项目概述
这是一个基于大语言模型的多功能智能助手应用，集成了聊天机器人、智能翻译、智能客服、知识库问答和竞赛信息提取等功能。

## 核心技术栈

### 🖥️ 前端框架
- **Streamlit 1.28.0**
  - 用于构建Web界面的Python框架
  - 提供交互式组件和实时响应
  - 支持多页面应用和自定义CSS样式

### 🧠 大语言模型
- **讯飞星火大模型 (SparkLLM)**
  - 版本：4.0Ultra
  - 通过WebSocket连接 (`wss://spark-api.xf-yun.com/v4.0/chat`)
  - 支持超时控制和重试机制
  - 配置了120秒默认超时和3次最大重试

### 🔍 向量数据库与检索
- **ChromaDB 1.0.12**
  - 用于存储和检索文档向量
  - 支持相似性搜索
  - 持久化存储在 `data/vector_db/chroma`

### 📊 文本嵌入模型
- **text2vec-base-chinese**
  - 本地部署的中文文本向量化模型
  - 基于HuggingFace Transformers
  - 支持CUDA加速（自动检测GPU可用性）

### 🔗 LangChain生态
- **langchain 0.3.25** - 核心框架
- **langchain_chroma 0.2.4** - ChromaDB集成
- **langchain_community 0.3.24** - 社区组件
- **langchain_core 0.3.63** - 核心组件

### 📄 文档处理
- **PyPDF 5.6.0** - PDF文件解析
- **python-docx** (通过Docx2txtLoader) - Word文档处理
- **RecursiveCharacterTextSplitter** - 智能文本分割
- 支持多种编码格式 (UTF-8, GBK, UTF-16, ISO-8859-1)

### 🤖 机器学习框架
- **PyTorch 2.1.2+cu118**
  - CUDA 11.8支持
  - 包含自定义补丁 (`torch_patch.py`)
- **Transformers 4.52.4** - HuggingFace模型库
- **sentence-transformers 4.1.0** - 句子嵌入模型

### 🛠️ 工具库
- **python-dotenv 1.1.0** - 环境变量管理
- **urllib3 2.4.0** - HTTP客户端
- **socket** - 网络超时控制

## 项目架构

### 📁 目录结构
```
├── app.py                    # 主应用入口
├── requirements.txt          # 依赖管理
├── torch_patch.py           # PyTorch补丁
├── utils/                   # 工具模块
│   ├── config.py           # 配置管理
│   ├── knowledge_base.py   # 知识库管理
│   ├── competition.py      # 竞赛信息提取
│   ├── translator.py       # 翻译功能
│   └── timeout.py          # 超时控制
├── data/                   # 数据目录
│   ├── knowledge/          # 知识文档
│   ├── vector_db/          # 向量数据库
│   └── competitions/       # 竞赛数据
├── local_models/           # 本地模型
│   └── text2vec-base-chinese/
└── static/                 # 静态资源
    ├── styles/style.css    # 自定义样式
    └── images/             # 图片资源
```

### 🔧 核心功能模块

#### 1. 聊天机器人
- 基于讯飞星火大模型
- 支持上下文记忆
- 实时流式对话

#### 2. 知识库问答
- 文档上传和解析 (PDF, DOCX, TXT, MD)
- 向量化存储和检索
- 基于RAG的问答系统

#### 3. 智能翻译
- 多语言支持 (中文、英文、日语、法语、德语、西班牙语)
- 基于大模型的上下文翻译

#### 4. 竞赛信息提取
- 智能解析竞赛文档
- 结构化信息提取
- JSON格式输出

#### 5. 智能客服
- 分类问题处理
- 专业领域回答

## 🔒 安全与性能特性

### 超时控制
- 全局网络超时：120秒
- LLM调用超时保护
- 装饰器模式的超时实现

### 错误处理
- 多层异常捕获
- 优雅降级机制
- 详细错误日志

### 性能优化
- 懒加载模式 (`CUDA_MODULE_LOADING=LAZY`)
- 向量数据库持久化
- 文档分块处理 (chunk_size=500, overlap=100)

### 隐私保护
- 禁用Streamlit分析追踪
- 阻止第三方数据收集
- 本地模型部署

## 🚀 部署要求

### 硬件要求
- **CPU**: 多核处理器
- **内存**: 建议8GB以上
- **GPU**: 可选，支持CUDA 11.8+
- **存储**: 至少5GB可用空间

### 软件环境
- **Python**: 3.9+
- **操作系统**: Windows/Linux/macOS
- **CUDA**: 11.8+ (可选)

### 环境变量
```bash
SPARK_APP_ID=your_app_id
SPARK_API_KEY=your_api_key
SPARK_API_SECRET=your_api_secret
STREAMLIT_ANALYTICS_ENABLED=false
STREAMLIT_SERVER_ENABLE_STATIC_FILE_WATCHER=false
STREAMLIT_HIDE_PYTHON_WARNINGS=1
CUDA_MODULE_LOADING=LAZY
```

## 📦 安装部署

### 1. 依赖安装
```bash
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple
```

### 2. 模型准备
- 下载text2vec-base-chinese模型到 `local_models/` 目录
- 确保模型文件完整性

### 3. 启动应用
```bash
streamlit run app.py
```

## 🔄 扩展性

### 模块化设计
- 松耦合的工具模块
- 可插拔的功能组件
- 标准化的接口设计

### 可扩展功能
- 支持新的文档格式
- 可集成其他大模型
- 可添加新的业务功能

## 📊 技术特色

1. **多模态文档处理**: 支持PDF、Word、文本等多种格式
2. **智能向量检索**: 基于语义相似性的文档检索
3. **超时保护机制**: 全方位的超时控制和错误处理
4. **本地化部署**: 支持完全离线运行
5. **响应式界面**: 自适应的Web界面设计
6. **高性能优化**: GPU加速和内存优化

---

*文档生成时间: 2024年*
*项目版本: 基于当前代码库分析*
