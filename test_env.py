import torch
import sentence_transformers
from transformers import AutoModel
import os
from pathlib import Path
import importlib

def get_absolute_path(relative_path):
    """将相对路径转换为绝对路径"""
    base_dir = Path(__file__).parent
    return (base_dir / relative_path).resolve()

print("=== 环境验证 ===")
print(f"PyTorch版本: {torch.__version__}")
print(f"CUDA可用: {torch.cuda.is_available()}")
if torch.cuda.is_available():
    print(f"当前设备: {torch.cuda.get_device_name(0)}")
print(f"sentence-transformers版本: {sentence_transformers.__version__}")

# 测试本地模型加载
model_path = get_absolute_path("local_models/text2vec-base-chinese")
try:
    model = AutoModel.from_pretrained(str(model_path))
    print(f"✅ 本地模型加载成功: {model_path}")
except Exception as e:
    print(f"❌ 模型加载失败: {str(e)}")
    print(f"模型路径: {model_path}")
    print(f"路径是否存在: {model_path.exists()}")

# 检查依赖模块
required_modules = [
    'docx',
    'unstructured',
    'langchain',
    'sentence_transformers',
    'chromadb',
    'transformers',
    'torch',
    'streamlit'
]

print("\n=== 模块检查 ===")
for module in required_modules:
    try:
        importlib.import_module(module)
        print(f"✅ {module} 已安装")
    except ImportError as e:
        print(f"❌ {module} 未安装: {str(e)}")

# 检查知识库文件
print("\n=== 知识库文件检查 ===")
knowledge_path = get_absolute_path("data/knowledge")
print(f"知识库路径: {knowledge_path}")
if knowledge_path.exists():
    print("知识库文件列表:")
    for file in sorted(os.listdir(knowledge_path)):
        print(f"- {file}")
else:
    print("❌ 知识库目录不存在")

# 测试文档加载
print("\n=== 文档加载测试 ===")
try:
    from utils.knowledge_base import get_loader
    
    test_file = get_absolute_path("data/knowledge/1. 简介 Introduction.md")
    print(f"测试文件: {test_file}")
    
    if test_file.exists():
        loader = get_loader(str(test_file))
        docs = loader.load()
        print(f"成功加载 {len(docs)} 个文档块")
        for doc in docs[:1]:
            print("内容示例:", doc.page_content[:200] + "...")
    else:
        print("❌ 测试文件不存在")
except Exception as e:
    print(f"文档加载测试失败: {str(e)}")

print("\n=== 环境测试完成 ===")

def test_competition_extraction():
    test_text = "全国大学生计算机设计大赛由教育部主办，报名截止2023年5月1日，比赛时间2023年7月..."
    from utils.competition import extract_competition_info
    result = extract_competition_info(test_text)
    print("测试结果:", result)

test_competition_extraction()