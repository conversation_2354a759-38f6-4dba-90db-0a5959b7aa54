Looking in indexes: https://pypi.tuna.tsinghua.edu.cn/simple
Requirement already satisfied: langchain-chroma in e:\anaconda\envs\spark\lib\site-packages (0.2.4)
Requirement already satisfied: chromadb in e:\anaconda\envs\spark\lib\site-packages (1.0.12)
Requirement already satisfied: langchain-core>=0.3.60 in e:\anaconda\envs\spark\lib\site-packages (from langchain-chroma) (0.3.63)
Requirement already satisfied: numpy>=1.26.0 in e:\anaconda\envs\spark\lib\site-packages (from langchain-chroma) (1.26.4)
Requirement already satisfied: build>=1.0.3 in e:\anaconda\envs\spark\lib\site-packages (from chromadb) (1.2.2.post1)
Requirement already satisfied: pydantic>=1.9 in e:\anaconda\envs\spark\lib\site-packages (from chromadb) (2.11.5)
Requirement already satisfied: fastapi==0.115.9 in e:\anaconda\envs\spark\lib\site-packages (from chromadb) (0.115.9)
Requirement already satisfied: uvicorn>=0.18.3 in e:\anaconda\envs\spark\lib\site-packages (from uvicorn[standard]>=0.18.3->chromadb) (0.34.2)
Requirement already satisfied: posthog>=2.4.0 in e:\anaconda\envs\spark\lib\site-packages (from chromadb) (4.2.0)
Requirement already satisfied: typing-extensions>=4.5.0 in e:\anaconda\envs\spark\lib\site-packages (from chromadb) (4.13.2)
Requirement already satisfied: onnxruntime>=1.14.1 in e:\anaconda\envs\spark\lib\site-packages (from chromadb) (1.19.2)
Requirement already satisfied: opentelemetry-api>=1.2.0 in e:\anaconda\envs\spark\lib\site-packages (from chromadb) (1.27.0)
Requirement already satisfied: opentelemetry-exporter-otlp-proto-grpc>=1.2.0 in e:\anaconda\envs\spark\lib\site-packages (from chromadb) (1.27.0)
Requirement already satisfied: opentelemetry-instrumentation-fastapi>=0.41b0 in e:\anaconda\envs\spark\lib\site-packages (from chromadb) (0.48b0)
Requirement already satisfied: opentelemetry-sdk>=1.2.0 in e:\anaconda\envs\spark\lib\site-packages (from chromadb) (1.27.0)
Requirement already satisfied: tokenizers>=0.13.2 in e:\anaconda\envs\spark\lib\site-packages (from chromadb) (0.21.1)
Requirement already satisfied: pypika>=0.48.9 in e:\anaconda\envs\spark\lib\site-packages (from chromadb) (0.48.9)
Requirement already satisfied: tqdm>=4.65.0 in e:\anaconda\envs\spark\lib\site-packages (from chromadb) (4.66.1)
Requirement already satisfied: overrides>=7.3.1 in e:\anaconda\envs\spark\lib\site-packages (from chromadb) (7.7.0)
Requirement already satisfied: importlib-resources in e:\anaconda\envs\spark\lib\site-packages (from chromadb) (6.5.2)
Requirement already satisfied: grpcio>=1.58.0 in e:\anaconda\envs\spark\lib\site-packages (from chromadb) (1.71.0)
Requirement already satisfied: bcrypt>=4.0.1 in e:\anaconda\envs\spark\lib\site-packages (from chromadb) (4.3.0)
Requirement already satisfied: typer>=0.9.0 in e:\anaconda\envs\spark\lib\site-packages (from chromadb) (0.16.0)
Requirement already satisfied: kubernetes>=28.1.0 in e:\anaconda\envs\spark\lib\site-packages (from chromadb) (32.0.1)
Requirement already satisfied: tenacity>=8.2.3 in e:\anaconda\envs\spark\lib\site-packages (from chromadb) (8.5.0)
Requirement already satisfied: pyyaml>=6.0.0 in e:\anaconda\envs\spark\lib\site-packages (from chromadb) (6.0.2)
Requirement already satisfied: mmh3>=4.0.1 in e:\anaconda\envs\spark\lib\site-packages (from chromadb) (5.1.0)
Requirement already satisfied: orjson>=3.9.12 in e:\anaconda\envs\spark\lib\site-packages (from chromadb) (3.10.18)
Requirement already satisfied: httpx>=0.27.0 in e:\anaconda\envs\spark\lib\site-packages (from chromadb) (0.28.1)
Requirement already satisfied: rich>=10.11.0 in e:\anaconda\envs\spark\lib\site-packages (from chromadb) (13.9.4)
Requirement already satisfied: jsonschema>=4.19.0 in e:\anaconda\envs\spark\lib\site-packages (from chromadb) (4.24.0)
Requirement already satisfied: starlette<0.46.0,>=0.40.0 in e:\anaconda\envs\spark\lib\site-packages (from fastapi==0.115.9->chromadb) (0.45.3)
Requirement already satisfied: annotated-types>=0.6.0 in e:\anaconda\envs\spark\lib\site-packages (from pydantic>=1.9->chromadb) (0.7.0)
Requirement already satisfied: pydantic-core==2.33.2 in e:\anaconda\envs\spark\lib\site-packages (from pydantic>=1.9->chromadb) (2.33.2)
Requirement already satisfied: typing-inspection>=0.4.0 in e:\anaconda\envs\spark\lib\site-packages (from pydantic>=1.9->chromadb) (0.4.1)
Requirement already satisfied: anyio<5,>=3.6.2 in e:\anaconda\envs\spark\lib\site-packages (from starlette<0.46.0,>=0.40.0->fastapi==0.115.9->chromadb) (4.9.0)
Requirement already satisfied: exceptiongroup>=1.0.2 in e:\anaconda\envs\spark\lib\site-packages (from anyio<5,>=3.6.2->starlette<0.46.0,>=0.40.0->fastapi==0.115.9->chromadb) (1.3.0)
Requirement already satisfied: idna>=2.8 in e:\anaconda\envs\spark\lib\site-packages (from anyio<5,>=3.6.2->starlette<0.46.0,>=0.40.0->fastapi==0.115.9->chromadb) (3.10)
Requirement already satisfied: sniffio>=1.1 in e:\anaconda\envs\spark\lib\site-packages (from anyio<5,>=3.6.2->starlette<0.46.0,>=0.40.0->fastapi==0.115.9->chromadb) (1.3.1)
Requirement already satisfied: packaging>=19.1 in e:\anaconda\envs\spark\lib\site-packages (from build>=1.0.3->chromadb) (23.2)
Requirement already satisfied: pyproject_hooks in e:\anaconda\envs\spark\lib\site-packages (from build>=1.0.3->chromadb) (1.2.0)
Requirement already satisfied: colorama in e:\anaconda\envs\spark\lib\site-packages (from build>=1.0.3->chromadb) (0.4.6)
Requirement already satisfied: importlib-metadata>=4.6 in e:\anaconda\envs\spark\lib\site-packages (from build>=1.0.3->chromadb) (6.11.0)
Requirement already satisfied: tomli>=1.1.0 in e:\anaconda\envs\spark\lib\site-packages (from build>=1.0.3->chromadb) (2.2.1)
Requirement already satisfied: certifi in e:\anaconda\envs\spark\lib\site-packages (from httpx>=0.27.0->chromadb) (2025.4.26)
Requirement already satisfied: httpcore==1.* in e:\anaconda\envs\spark\lib\site-packages (from httpx>=0.27.0->chromadb) (1.0.9)
Requirement already satisfied: h11>=0.16 in e:\anaconda\envs\spark\lib\site-packages (from httpcore==1.*->httpx>=0.27.0->chromadb) (0.16.0)
Requirement already satisfied: zipp>=0.5 in e:\anaconda\envs\spark\lib\site-packages (from importlib-metadata>=4.6->build>=1.0.3->chromadb) (3.22.0)
Requirement already satisfied: attrs>=22.2.0 in e:\anaconda\envs\spark\lib\site-packages (from jsonschema>=4.19.0->chromadb) (25.3.0)
Requirement already satisfied: jsonschema-specifications>=2023.03.6 in e:\anaconda\envs\spark\lib\site-packages (from jsonschema>=4.19.0->chromadb) (2025.4.1)
Requirement already satisfied: referencing>=0.28.4 in e:\anaconda\envs\spark\lib\site-packages (from jsonschema>=4.19.0->chromadb) (0.36.2)
Requirement already satisfied: rpds-py>=0.7.1 in e:\anaconda\envs\spark\lib\site-packages (from jsonschema>=4.19.0->chromadb) (0.25.1)
Requirement already satisfied: six>=1.9.0 in e:\anaconda\envs\spark\lib\site-packages (from kubernetes>=28.1.0->chromadb) (1.17.0)
Requirement already satisfied: python-dateutil>=2.5.3 in e:\anaconda\envs\spark\lib\site-packages (from kubernetes>=28.1.0->chromadb) (2.9.0.post0)
Requirement already satisfied: google-auth>=1.0.1 in e:\anaconda\envs\spark\lib\site-packages (from kubernetes>=28.1.0->chromadb) (2.40.2)
Requirement already satisfied: websocket-client!=0.40.0,!=0.41.*,!=0.42.*,>=0.32.0 in e:\anaconda\envs\spark\lib\site-packages (from kubernetes>=28.1.0->chromadb) (1.8.0)
Requirement already satisfied: requests in e:\anaconda\envs\spark\lib\site-packages (from kubernetes>=28.1.0->chromadb) (2.32.3)
Requirement already satisfied: requests-oauthlib in e:\anaconda\envs\spark\lib\site-packages (from kubernetes>=28.1.0->chromadb) (2.0.0)
Requirement already satisfied: oauthlib>=3.2.2 in e:\anaconda\envs\spark\lib\site-packages (from kubernetes>=28.1.0->chromadb) (3.2.2)
Requirement already satisfied: urllib3>=1.24.2 in e:\anaconda\envs\spark\lib\site-packages (from kubernetes>=28.1.0->chromadb) (2.4.0)
Requirement already satisfied: durationpy>=0.7 in e:\anaconda\envs\spark\lib\site-packages (from kubernetes>=28.1.0->chromadb) (0.10)
Requirement already satisfied: cachetools<6.0,>=2.0.0 in e:\anaconda\envs\spark\lib\site-packages (from google-auth>=1.0.1->kubernetes>=28.1.0->chromadb) (5.5.2)
Requirement already satisfied: pyasn1-modules>=0.2.1 in e:\anaconda\envs\spark\lib\site-packages (from google-auth>=1.0.1->kubernetes>=28.1.0->chromadb) (0.4.2)
Requirement already satisfied: rsa<5,>=3.1.4 in e:\anaconda\envs\spark\lib\site-packages (from google-auth>=1.0.1->kubernetes>=28.1.0->chromadb) (4.9.1)
Requirement already satisfied: pyasn1>=0.1.3 in e:\anaconda\envs\spark\lib\site-packages (from rsa<5,>=3.1.4->google-auth>=1.0.1->kubernetes>=28.1.0->chromadb) (0.6.1)
Requirement already satisfied: langsmith<0.4,>=0.1.126 in e:\anaconda\envs\spark\lib\site-packages (from langchain-core>=0.3.60->langchain-chroma) (0.1.147)
Requirement already satisfied: jsonpatch<2.0,>=1.33 in e:\anaconda\envs\spark\lib\site-packages (from langchain-core>=0.3.60->langchain-chroma) (1.33)
Requirement already satisfied: jsonpointer>=1.9 in e:\anaconda\envs\spark\lib\site-packages (from jsonpatch<2.0,>=1.33->langchain-core>=0.3.60->langchain-chroma) (3.0.0)
Requirement already satisfied: requests-toolbelt<2.0.0,>=1.0.0 in e:\anaconda\envs\spark\lib\site-packages (from langsmith<0.4,>=0.1.126->langchain-core>=0.3.60->langchain-chroma) (1.0.0)
Requirement already satisfied: charset-normalizer<4,>=2 in e:\anaconda\envs\spark\lib\site-packages (from requests->kubernetes>=28.1.0->chromadb) (3.4.2)
Requirement already satisfied: coloredlogs in e:\anaconda\envs\spark\lib\site-packages (from onnxruntime>=1.14.1->chromadb) (15.0.1)
Requirement already satisfied: flatbuffers in e:\anaconda\envs\spark\lib\site-packages (from onnxruntime>=1.14.1->chromadb) (25.2.10)
Requirement already satisfied: protobuf in e:\anaconda\envs\spark\lib\site-packages (from onnxruntime>=1.14.1->chromadb) (4.25.8)
Requirement already satisfied: sympy in e:\anaconda\envs\spark\lib\site-packages (from onnxruntime>=1.14.1->chromadb) (1.14.0)
Requirement already satisfied: deprecated>=1.2.6 in e:\anaconda\envs\spark\lib\site-packages (from opentelemetry-api>=1.2.0->chromadb) (1.2.18)
Requirement already satisfied: wrapt<2,>=1.10 in e:\anaconda\envs\spark\lib\site-packages (from deprecated>=1.2.6->opentelemetry-api>=1.2.0->chromadb) (1.17.2)
Requirement already satisfied: googleapis-common-protos~=1.52 in e:\anaconda\envs\spark\lib\site-packages (from opentelemetry-exporter-otlp-proto-grpc>=1.2.0->chromadb) (1.70.0)
Requirement already satisfied: opentelemetry-exporter-otlp-proto-common==1.27.0 in e:\anaconda\envs\spark\lib\site-packages (from opentelemetry-exporter-otlp-proto-grpc>=1.2.0->chromadb) (1.27.0)
Requirement already satisfied: opentelemetry-proto==1.27.0 in e:\anaconda\envs\spark\lib\site-packages (from opentelemetry-exporter-otlp-proto-grpc>=1.2.0->chromadb) (1.27.0)
Requirement already satisfied: opentelemetry-semantic-conventions==0.48b0 in e:\anaconda\envs\spark\lib\site-packages (from opentelemetry-sdk>=1.2.0->chromadb) (0.48b0)
Requirement already satisfied: opentelemetry-instrumentation-asgi==0.48b0 in e:\anaconda\envs\spark\lib\site-packages (from opentelemetry-instrumentation-fastapi>=0.41b0->chromadb) (0.48b0)
Requirement already satisfied: opentelemetry-instrumentation==0.48b0 in e:\anaconda\envs\spark\lib\site-packages (from opentelemetry-instrumentation-fastapi>=0.41b0->chromadb) (0.48b0)
Requirement already satisfied: opentelemetry-util-http==0.48b0 in e:\anaconda\envs\spark\lib\site-packages (from opentelemetry-instrumentation-fastapi>=0.41b0->chromadb) (0.48b0)
Requirement already satisfied: setuptools>=16.0 in e:\anaconda\envs\spark\lib\site-packages (from opentelemetry-instrumentation==0.48b0->opentelemetry-instrumentation-fastapi>=0.41b0->chromadb) (80.8.0)
Requirement already satisfied: asgiref~=3.0 in e:\anaconda\envs\spark\lib\site-packages (from opentelemetry-instrumentation-asgi==0.48b0->opentelemetry-instrumentation-fastapi>=0.41b0->chromadb) (3.8.1)
Requirement already satisfied: backoff>=1.10.0 in e:\anaconda\envs\spark\lib\site-packages (from posthog>=2.4.0->chromadb) (2.2.1)
Requirement already satisfied: distro>=1.5.0 in e:\anaconda\envs\spark\lib\site-packages (from posthog>=2.4.0->chromadb) (1.9.0)
Requirement already satisfied: markdown-it-py>=2.2.0 in e:\anaconda\envs\spark\lib\site-packages (from rich>=10.11.0->chromadb) (3.0.0)
Requirement already satisfied: pygments<3.0.0,>=2.13.0 in e:\anaconda\envs\spark\lib\site-packages (from rich>=10.11.0->chromadb) (2.19.1)
Requirement already satisfied: mdurl~=0.1 in e:\anaconda\envs\spark\lib\site-packages (from markdown-it-py>=2.2.0->rich>=10.11.0->chromadb) (0.1.2)
Requirement already satisfied: huggingface-hub<1.0,>=0.16.4 in e:\anaconda\envs\spark\lib\site-packages (from tokenizers>=0.13.2->chromadb) (0.32.3)
Requirement already satisfied: filelock in e:\anaconda\envs\spark\lib\site-packages (from huggingface-hub<1.0,>=0.16.4->tokenizers>=0.13.2->chromadb) (3.18.0)
Requirement already satisfied: fsspec>=2023.5.0 in e:\anaconda\envs\spark\lib\site-packages (from huggingface-hub<1.0,>=0.16.4->tokenizers>=0.13.2->chromadb) (2024.3.1)
Requirement already satisfied: click>=8.0.0 in e:\anaconda\envs\spark\lib\site-packages (from typer>=0.9.0->chromadb) (8.1.8)
Requirement already satisfied: shellingham>=1.3.0 in e:\anaconda\envs\spark\lib\site-packages (from typer>=0.9.0->chromadb) (1.5.4)
Requirement already satisfied: httptools>=0.6.3 in e:\anaconda\envs\spark\lib\site-packages (from uvicorn[standard]>=0.18.3->chromadb) (0.6.4)
Requirement already satisfied: python-dotenv>=0.13 in e:\anaconda\envs\spark\lib\site-packages (from uvicorn[standard]>=0.18.3->chromadb) (1.0.0)
Requirement already satisfied: watchfiles>=0.13 in e:\anaconda\envs\spark\lib\site-packages (from uvicorn[standard]>=0.18.3->chromadb) (1.0.5)
Requirement already satisfied: websockets>=10.4 in e:\anaconda\envs\spark\lib\site-packages (from uvicorn[standard]>=0.18.3->chromadb) (15.0.1)
Requirement already satisfied: humanfriendly>=9.1 in e:\anaconda\envs\spark\lib\site-packages (from coloredlogs->onnxruntime>=1.14.1->chromadb) (10.0)
Requirement already satisfied: pyreadline3 in e:\anaconda\envs\spark\lib\site-packages (from humanfriendly>=9.1->coloredlogs->onnxruntime>=1.14.1->chromadb) (3.5.4)
Requirement already satisfied: mpmath<1.4,>=1.1.0 in e:\anaconda\envs\spark\lib\site-packages (from sympy->onnxruntime>=1.14.1->chromadb) (1.3.0)
