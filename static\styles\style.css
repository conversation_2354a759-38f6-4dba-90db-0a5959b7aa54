/* ===== 基础重置 ===== */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: "Segoe UI", "PingFang SC", "Microsoft YaHei", sans-serif;
}

/* ===== Streamlit 主容器 ===== */
.stApp {
  background-color: #ffffff;
  color: #333333;
  line-height: 1.6;
}

/* ===== 侧边栏深度优化 ===== */
.stSidebar {
  background-color: #ffffff !important;
  border-right: 1px solid #eaeaea !important;
  padding: 1.5rem 1rem !important;
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.03) !important;
}

/* 侧边栏标题 */
.stSidebar .stMarkdown h1 {
  color: #1a1a1a !important;
  font-size: 1.5rem !important;
  font-weight: 600 !important;
  margin-bottom: 1.5rem !important;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #f0f0f0;
}

/* 单选按钮组样式 */
.stSidebar .stRadio > label {
  font-size: 0.95rem !important;
  color: #555 !important;
  transition: all 0.2s;
  margin: 0.3rem 0 !important;
  padding: 0.6rem 1rem !important;
  border-radius: 8px !important;
}

.stSidebar .stRadio > label:hover {
  background-color: #f8f8f8 !important;
}

.stSidebar .stRadio > div[role="radiogroup"] {
  gap: 0.3rem !important;
}

/* 选中状态 */
.stSidebar .stRadio > label[data-baseweb="radio"]:has(input:checked) {
  background-color: #f0f7ff !important;
  color: #0068e5 !important;
  font-weight: 500 !important;
}

/* 上传组件美化 */
.stSidebar .stFileUploader {
  border: 1px dashed #ddd !important;
  border-radius: 8px !important;
  padding: 1rem !important;
  background-color: #fafafa !important;
}

.stSidebar .stFileUploader:hover {
  border-color: #0068e5 !important;
}

/* ===== 主内容区优化 ===== */
.stChatMessage {
  max-width: 85% !important;
  padding: 1rem 1.2rem !important;
  border-radius: 12px !important;
  margin-bottom: 1rem !important;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

/* 用户消息 */
.user-message {
  background-color: #f0f7ff !important;
  margin-left: auto !important;
  border-bottom-right-radius: 4px !important;
}

/* AI消息 */
.assistant-message {
  background-color: #f8f8f8 !important;
  margin-right: auto !important;
  border-bottom-left-radius: 4px !important;
}

/* 输入框优化 */
.stTextInput > div > div > input {
  border: 1px solid #e0e0e0 !important;
  border-radius: 8px !important;
  padding: 0.8rem 1rem !important;
  font-size: 0.95rem !important;
}

.stTextInput > div > div > input:focus {
  border-color: #0068e5 !important;
  box-shadow: 0 0 0 2px rgba(0, 104, 229, 0.2) !important;
}

/* 按钮优化 */
.stButton > button {
  background-color: #0068e5 !important;
  color: white !important;
  border-radius: 8px !important;
  padding: 0.7rem 1.5rem !important;
  font-weight: 500 !important;
  transition: all 0.2s;
  border: none !important;
}

.stButton > button:hover {
  background-color: #0056b3 !important;
  transform: translateY(-1px);
}

/* ===== 响应式优化 ===== */
@media (max-width: 768px) {
  .stSidebar {
    width: 100% !important;
    padding: 1rem !important;
  }

  .stChatMessage {
    max-width: 95% !important;
  }
}
