from langchain_community.embeddings import HuggingFaceEmbeddings
from langchain_chroma import Chroma
from chromadb.config import Settings
from langchain_community.document_loaders import TextLoader, PyPDFLoader, Docx2txtLoader
from langchain.text_splitter import RecursiveCharacterTextSplitter
import os
from pathlib import Path
import logging
import torch

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def get_absolute_path(relative_path):
    """将相对路径转换为绝对路径"""
    base_dir = Path(__file__).parent.parent
    return (base_dir / relative_path).resolve()

class UniversalTextLoader:
    """万能文本加载器，处理所有文本类文件"""
    def __init__(self, file_path: str):
        self.file_path = file_path
    
    def load(self):
        encodings = ['utf-8', 'gbk', 'utf-16', 'iso-8859-1']
        last_error = None
        
        for encoding in encodings:
            try:
                with open(self.file_path, 'r', encoding=encoding) as f:
                    content = f.read()
                from langchain_core.documents import Document
                return [Document(
                    page_content=content,
                    metadata={"source": os.path.basename(self.file_path)}
                )]
            except UnicodeDecodeError as e:
                last_error = e
                continue
            except Exception as e:
                last_error = e
                continue
        
        # 所有编码尝试失败，使用二进制读取
        try:
            with open(self.file_path, 'rb') as f:
                content = f.read().decode('utf-8', errors='replace')
            from langchain_core.documents import Document
            return [Document(
                page_content=content,
                metadata={
                    "source": os.path.basename(self.file_path),
                    "warning": f"使用替换错误解码: {str(last_error)}"
                }
            )]
        except Exception as e:
            from langchain_core.documents import Document
            return [Document(
                page_content="",
                metadata={
                    "source": os.path.basename(self.file_path),
                    "error": f"无法加载文件: {str(e)}"
                }
            )]

def get_loader(file_path: str):
    """
    超稳定文件加载器
    支持PDF、Word和所有文本文件
    """
    try:
        # PDF文件
        if file_path.lower().endswith('.pdf'):
            return PyPDFLoader(file_path)
        
        # Word文档
        elif file_path.lower().endswith('.docx'):
            return Docx2txtLoader(file_path)
        
        # 其他所有文本文件（包括Markdown）
        else:
            return UniversalTextLoader(file_path)
            
    except Exception as e:
        logger.error(f"创建加载器失败，使用万能加载器: {str(e)}")
        return UniversalTextLoader(file_path)

def init_knowledge_base():
    """
    初始化知识库 - 终极稳定版本
    """
    try:
        # 1. 检查模型路径
        model_path = get_absolute_path("local_models/text2vec-base-chinese")
        if not model_path.exists():
            raise FileNotFoundError(f"模型路径不存在: {model_path}")
        
        # 2. 初始化嵌入模型
        device = "cuda" if torch.cuda.is_available() else "cpu"
        embeddings = HuggingFaceEmbeddings(
            model_name=str(model_path),
            model_kwargs={"device": device},
            encode_kwargs={"normalize_embeddings": False}
        )

        # 3. 加载文档
        knowledge_path = get_absolute_path("data/knowledge")
        if not knowledge_path.exists():
            knowledge_path.mkdir(parents=True)
            logger.warning(f"知识目录不存在，已创建: {knowledge_path}")
            return Chroma(embedding_function=embeddings)
        
        documents = []
        for filename in sorted(os.listdir(knowledge_path)):
            file_path = os.path.normpath(os.path.join(knowledge_path, filename))
            try:
                loader = get_loader(file_path)
                loaded_docs = loader.load()
                
                # 确保文档格式正确
                for doc in loaded_docs:
                    if not hasattr(doc, 'metadata') or not isinstance(doc.metadata, dict):
                        doc.metadata = {}
                    doc.metadata["source"] = filename
                
                documents.extend(loaded_docs)
                logger.info(f"已加载文件: {filename}")
                
            except Exception as e:
                logger.error(f"文件 {filename} 加载异常: {str(e)}")
                from langchain_core.documents import Document
                documents.append(Document(
                    page_content="",
                    metadata={"source": filename, "error": str(e)}
                ))

        # 4. 文本分割
        text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=500,
            chunk_overlap=100,
            length_function=len,
            is_separator_regex=False
        )
        texts = text_splitter.split_documents(documents) if documents else []

        # 5. 创建向量数据库
        persist_path = get_absolute_path("data/vector_db/chroma")
        os.makedirs(persist_path, exist_ok=True)
        
        return Chroma.from_documents(
            documents=texts,
            embedding=embeddings,
            persist_directory=str(persist_path)
        )  # 这里添加了缺少的括号
        
    except Exception as e:
        logger.error(f"知识库初始化失败: {str(e)}")
        # 终极回退方案
        from langchain_community.embeddings import FakeEmbeddings
        return Chroma(embedding_function=FakeEmbeddings(size=768))

def query_knowledge_base(query, vectordb):
    """查询知识库（带超时保护）"""
    if not vectordb:
        return "知识库未初始化，请先上传文档"
    
    try:
        # 添加超时装饰器
        from utils.timeout import timeout
        
        @timeout(seconds=120)
        def perform_query():
            docs = vectordb.similarity_search(query, k=3)
            if not docs:
                return "未找到相关答案"
            
            context = "\n".join([
                f"来源: {doc.metadata.get('source', '未知')}\n内容: {doc.page_content}" 
                for doc in docs
            ])
            
            from utils.config import get_spark_llm
            llm = get_spark_llm()
            return llm(
                f"根据以下上下文回答问题:\n{context}\n\n问题: {query}\n\n"
                "请用中文给出专业回答，如果上下文不包含答案，请回答'根据已有信息无法回答该问题'"
            )
        
        return perform_query()
        
    except TimeoutError:
        return "查询超时，请简化问题或稍后重试"
    except Exception as e:
        logger.error(f"查询出错: {str(e)}")
        return f"查询过程中出错: {str(e)}"