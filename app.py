MAX_INPUT_LENGTH = 3500  # 在app.py的顶部添加
from utils.competition import extract_competition_info, MAX_INPUT_LENGTH
from utils.config import get_spark_llm
from utils.knowledge_base import init_knowledge_base, query_knowledge_base, get_absolute_path
import torch_patch 
import os
# 禁用所有分析追踪
os.environ["STREAMLIT_ANALYTICS_ENABLED"] = "false"
os.environ["STREAMLIT_SERVER_ENABLE_STATIC_FILE_WATCHER"] = "false"
os.environ["STREAMLIT_HIDE_PYTHON_WARNINGS"] = "1"
os.environ["CUDA_MODULE_LOADING"] = "LAZY"

# 隐藏警告
import warnings
warnings.filterwarnings("ignore", category=UserWarning, module="torch._classes")
warnings.filterwarnings("ignore", category=UserWarning)
warnings.filterwarnings("ignore", category=DeprecationWarning)

import torch_patch  # 确保先导入补丁
import streamlit as st
from dotenv import load_dotenv
from utils.config import get_spark_llm
from utils.knowledge_base import init_knowledge_base
from utils.translator import translate_text
from utils.competition import extract_competition_info
# 在现有导入基础上添加
from utils.knowledge_base import init_knowledge_base, query_knowledge_base
from utils.competition import extract_competition_info
from utils.translator import translate_text
from utils.knowledge_base import get_absolute_path
import socket
import urllib3
from utils.timeout import timeout, TimeoutError
# 全局网络超时设置（单位：秒）
NETWORK_TIMEOUT = 120  
socket.setdefaulttimeout(NETWORK_TIMEOUT)
urllib3.disable_warnings()
urllib3.util.timeout.Timeout(connect=NETWORK_TIMEOUT, read=NETWORK_TIMEOUT)
# 在初始化部分添加目录检查
def check_directories():
    required_dirs = [
        "data/knowledge",
        "data/vector_db/chroma",  # 修改路径以匹配实际结构
        "local_models/text2vec-base-chinese"
    ]
    
    for rel_path in required_dirs:
        abs_path = get_absolute_path(rel_path)
        if not abs_path.exists():
            abs_path.mkdir(parents=True, exist_ok=True)
            print(f"创建目录: {abs_path}")
# 初始化环境变量
load_dotenv()
check_directories()
# 设置页面配置
st.set_page_config(
    page_title="智能助手",
    page_icon="🤖",
    layout="wide",
    initial_sidebar_state="expanded"
)
# 在 app.py 的页面配置后添加
st.markdown("""
<meta name="segment" content="no">
<script>
    !function(){var analytics=window.analytics=window.analytics||[];if(!analytics.initialize)if(analytics.invoked)window.console&&console.error&&console.error("Segment snippet included twice.");else{analytics.invoked=!0;analytics.methods=["trackSubmit","trackClick","trackLink","trackForm","pageview","identify","reset","group","track","ready","alias","debug","page","once","off","on"];analytics.factory=function(t){return function(){var e=Array.prototype.slice.call(arguments);e.unshift(t);analytics.push(e);return analytics}};analytics.load=function(){};
    }}();
</script>
""", unsafe_allow_html=True)
# 初始化知识库
# 在初始化部分添加错误处理
try:
    vectordb = init_knowledge_base()
except Exception as e:
    st.error(f"知识库初始化失败: {str(e)}")
    vectordb = None

# 自定义CSS样式
def local_css(file_name):
    try:
        with open(file_name, "r", encoding="utf-8") as f:  # 明确指定使用utf-8编码
            st.markdown(f"<style>{f.read()}</style>", unsafe_allow_html=True)
    except UnicodeDecodeError:
        with open(file_name, "r", encoding="gbk") as f:  # 如果utf-8失败，尝试gbk
            st.markdown(f"<style>{f.read()}</style>", unsafe_allow_html=True)
    except Exception as e:
        st.error(f"加载CSS文件失败: {str(e)}")

# 加载自定义样式
local_css("static/styles/style.css")

# 初始化会话状态
if "messages" not in st.session_state:
    st.session_state.messages = []

# 侧边栏
with st.sidebar:
    st.title("功能选择")
    app_mode = st.radio(
        "选择功能模式:",
        ["聊天机器人", "智能翻译", "智能客服", "知识库问答", "竞赛信息提取"],
        index=0
    )
        
        # 在侧边栏部分修改文件上传器
    if app_mode == "知识库问答":
        st.info("知识库问答功能可以回答基于上传文档的问题")
        uploaded_file = st.file_uploader("上传知识文档", type=["pdf", "txt", "docx", "md"])
        if uploaded_file:
            file_path = os.path.join("data/knowledge", uploaded_file.name)
            with open(file_path, "wb") as f:
                f.write(uploaded_file.getbuffer())
            st.success("文件上传成功!")
            try:
                vectordb = init_knowledge_base()  # 重新加载知识库
            except Exception as e:
                st.error(f"知识库重新加载失败: {str(e)}")
    
    elif app_mode == "竞赛信息提取":
        st.info("上传竞赛文档或粘贴文本以提取关键信息")
        comp_file = st.file_uploader("上传竞赛文档", type=["pdf", "txt", "docx"])
        comp_text = st.text_area("或直接粘贴竞赛文本")

# 主界面
st.title("🤖 智能助手")
st.caption("一个多功能的大模型应用")

# 聊天历史显示
for message in st.session_state.messages:
    with st.chat_message(message["role"], avatar=message["avatar"]):
        st.markdown(message["content"])

# 根据选择的功能模式显示不同界面
if app_mode == "聊天机器人":
    if prompt := st.chat_input("你想聊些什么?"):
        st.session_state.messages.append({"role": "user", "content": prompt, "avatar": "👤"})
        
        with st.chat_message("user", avatar="👤"):
            st.markdown(prompt)
        
        with st.chat_message("assistant", avatar="🤖"):
            try:
                # 添加超时保护
                from utils.timeout import timeout
                
                @timeout(seconds=120)
                def get_llm_response():
                    llm = get_spark_llm()
                    return llm(prompt)
                
                response = get_llm_response()
                st.markdown(response)
                st.session_state.messages.append({"role": "assistant", "content": response, "avatar": "🤖"})
                
            except TimeoutError:
                st.error("请求超时，请简化问题或稍后重试")
                st.session_state.messages.append({"role": "assistant", "content": "请求超时，请简化问题或稍后重试", "avatar": "🤖"})
            except Exception as e:
                st.error(f"发生错误: {str(e)}")
                st.session_state.messages.append({"role": "assistant", "content": f"发生错误: {str(e)}", "avatar": "🤖"})

elif app_mode == "智能翻译":
    st.subheader("智能翻译")
    col1, col2 = st.columns(2)
    with col1:
        source_text = st.text_area("输入要翻译的文本", height=200)
        target_lang = st.selectbox("目标语言", ["英文", "中文", "日语", "法语", "德语", "西班牙语"])
    with col2:
        if st.button("翻译"):
            if source_text:
                with st.spinner("翻译中..."):
                    translated = translate_text(source_text, target_lang)
                    st.text_area("翻译结果", translated, height=200)
            else:
                st.warning("请输入要翻译的文本")

elif app_mode == "智能客服":
    st.subheader("智能客服")
    faq_options = [
        "产品功能咨询",
        "价格与订阅",
        "技术支持",
        "账户问题",
        "其他问题"
    ]
    selected_faq = st.selectbox("常见问题分类", faq_options)
    user_question = st.text_input("您的问题")
    
    if st.button("提交"):
        if user_question:
            with st.spinner("思考中..."):
                llm = get_spark_llm()
                prompt = f"作为{selected_faq}的客服代表，专业且友好地回答以下问题:\n\n{user_question}"
                response = llm(prompt)
                st.success(response)
        else:
            st.warning("请输入您的问题")

elif app_mode == "知识库问答":
    st.subheader("知识库问答")
    if prompt := st.chat_input("关于知识文档的问题?"):
        st.session_state.messages.append({"role": "user", "content": prompt, "avatar": "👤"})
        with st.chat_message("user", avatar="👤"):
            st.markdown(prompt)
        
        with st.chat_message("assistant", avatar="📚"):
            response = query_knowledge_base(prompt, vectordb)
            st.markdown(response)
            st.session_state.messages.append({"role": "assistant", "content": response, "avatar": "📚"})

elif app_mode == "竞赛信息提取":
    st.subheader("竞赛信息提取")
    
    # 添加使用说明
    with st.expander("📌 使用提示"):
        st.markdown("""
        1. 优先上传PDF/Word格式文件
        2. 大文件建议先提取关键页
        3. 文本直接粘贴时只需包含:
           - 竞赛名称
           - 主办方
           - 重要日期
           - 核心要求
        """)

    comp_file = st.file_uploader("上传竞赛文档", type=["pdf", "txt", "docx"], 
                               help="建议文件小于50KB")
    comp_text = st.text_area("或直接粘贴关键文本", height=150,
                           placeholder="只需粘贴包含关键信息的部分...")

    if st.button("提取信息", type="primary"):
        if comp_file or comp_text:
            with st.spinner("正在智能提取..."):
                try:
                    if comp_file:
                        # 使用pypdf等库直接提取文本
                        if comp_file.type == "application/pdf":
                            from pypdf import PdfReader
                            reader = PdfReader(comp_file)
                            text = "\n".join([page.extract_text() for page in reader.pages])
                        else:
                            text = comp_file.getvalue().decode('utf-8', errors='replace')
                    else:
                        text = comp_text

                    result = extract_competition_info(text)
                    
                    if "error" in result:
                        st.error(f"❌ {result['error']}")
                        st.info(f"💡 解决方案: {result['solution']}")
                        if "actual_length" in result:
                            progress_value = min(result['actual_length'] / 3500, 1.0)  # 直接使用3500代替MAX_INPUT_LENGTH
                            st.progress(progress_value)
                    else:
                        st.success("✅ 信息提取成功")
                        st.json(result)
                        
                except Exception as e:
                    st.error(f"系统错误: {str(e)}")
                    st.stop()
        else:
            st.warning("请上传文件或粘贴文本")